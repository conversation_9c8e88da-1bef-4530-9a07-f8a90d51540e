#!/usr/bin/env python
"""
简化的新代码训练测试
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime
import traceback
import warnings
warnings.filterwarnings('ignore')

# 确保新代码路径在最前面
current_dir = os.path.dirname(os.path.abspath(__file__))
train_model_path = os.path.join(current_dir, 'dashboard', 'DFM', 'train_model')
data_prep_path = os.path.join(current_dir, 'dashboard', 'DFM', 'data_prep')

# 清理可能的模块缓存
modules_to_clear = [k for k in sys.modules.keys() if any(x in k for x in ['tune_dfm', 'variable_selection', 'data_preparation'])]
for module_name in modules_to_clear:
    if module_name in sys.modules:
        del sys.modules[module_name]
        print(f"🧹 清理模块缓存: {module_name}")

# 移除老代码路径
paths_to_remove = [p for p in sys.path if 'old' in p]
for path in paths_to_remove:
    sys.path.remove(path)
    print(f"🗑️ 移除老代码路径: {path}")

# 将新代码路径插入到最前面
sys.path.insert(0, train_model_path)
sys.path.insert(0, data_prep_path)
print(f"📁 新代码训练路径: {train_model_path}")
print(f"📁 新代码数据路径: {data_prep_path}")

def test_new_code_training():
    """测试新代码训练功能"""
    try:
        print("🚀 开始新代码训练测试")
        print("="*50)
        
        # 步骤1：数据准备
        print("📊 步骤1：数据准备...")
        from data_preparation import prepare_data
        
        result = prepare_data(
            excel_path='data/经济数据库0508.xlsx',
            target_sheet_name='工业增加值同比增速_月度_同花顺',
            target_variable_name='规模以上工业增加值:当月同比',
            target_freq='W-FRI',
            consecutive_nan_threshold=10,
            data_start_date='2020-01-01',
            data_end_date=None
        )
        
        training_data = result[0]
        target_var = '规模以上工业增加值:当月同比'
        predictor_vars = [col for col in training_data.columns if col != target_var]
        
        print(f"✅ 数据准备完成")
        print(f"📊 数据形状: {training_data.shape}")
        print(f"📊 预测变量数: {len(predictor_vars)}")
        print(f"📊 目标变量: {target_var}")
        
        # 步骤2：模型训练
        print("\n🏗️ 步骤2：模型训练...")
        from tune_dfm import train_and_save_dfm_results
        
        # 设置训练参数
        training_params = {
            'input_df': training_data,
            'target_variable': target_var,
            'selected_indicators': predictor_vars,
            'training_start_date': '2020-01-01',
            'training_end_date': '2024-06-28',
            'validation_start_date': '2024-06-29',
            'validation_end_date': '2024-12-27',
            'n_factors': 10,  # 会被Bai-Ng覆盖
            'em_max_iter': 30,  # 减少迭代次数以加快测试
            'enable_hyperparameter_tuning': False,
            'use_bai_ng_factor_selection': True,
            'enable_variable_selection': True,
            'variable_selection_method': 'global_backward',
            'enable_detailed_analysis': False,
            'generate_excel_report': False,
            'output_base_dir': 'dashboard/DFM/outputs',
            'max_workers': 2  # 减少并行数以避免资源问题
        }
        
        print("📋 训练参数:")
        for key, value in training_params.items():
            if key not in ['input_df', 'selected_indicators']:  # 跳过大对象
                print(f"  {key}: {value}")
        
        print("\n🔄 开始训练...")
        results = train_and_save_dfm_results(**training_params)
        
        if results:
            print("✅ 新代码训练成功！")
            print(f"📊 返回结果类型: {type(results)}")
            if isinstance(results, dict):
                print(f"📊 结果键: {list(results.keys())}")
            return True
        else:
            print("❌ 新代码训练失败")
            return False
            
    except Exception as e:
        print(f"❌ 新代码训练测试失败: {e}")
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = test_new_code_training()
    if success:
        print("\n🎉 新代码训练测试成功完成！")
    else:
        print("\n💥 新代码训练测试失败！")
