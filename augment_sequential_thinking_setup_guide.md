# Augment + Sequential Thinking MCP 配置指南

## 前提条件

确保您已经安装了Sequential Thinking MCP服务器：
```bash
npm install -g @modelcontextprotocol/server-sequential-thinking
```

## 配置方法

### 方法1：使用Augment设置面板（推荐）

1. 打开VS Code中的Augment插件
2. 点击Augment面板右上角的齿轮图标⚙️
3. 在MCP服务器部分，点击`+`按钮
4. 填写配置：
   - **Name**: `sequential-thinking`
   - **Command**: `npx`
   - **Args**: `@modelcontextprotocol/server-sequential-thinking`

### 方法2：编辑settings.json

1. 在VS Code中按 `Ctrl+Shift+P`
2. 输入 "Augment: Edit Settings"
3. 选择 "Edit in settings.json"
4. 添加以下配置：

```json
{
    "augment.advanced": {
        "mcpServers": [
            {
                "name": "sequential-thinking",
                "command": "npx",
                "args": ["@modelcontextprotocol/server-sequential-thinking"]
            }
        ]
    }
}
```

## 验证配置

1. 重启VS Code
2. 打开Augment面板
3. 检查是否有错误消息
4. 在Augment Agent中测试Sequential Thinking功能

## 故障排除

### 常见问题

1. **MCP服务器未启动**
   - 检查Node.js和npm是否正确安装
   - 验证Sequential Thinking包是否安装：`npm list -g @modelcontextprotocol/server-sequential-thinking`

2. **配置语法错误**
   - 确保JSON格式正确
   - 检查括号和逗号

3. **权限问题**
   - 确保有执行npx命令的权限
   - 在Windows上可能需要管理员权限

### 测试命令

验证Sequential Thinking MCP是否工作：
```bash
npx @modelcontextprotocol/server-sequential-thinking --help
```

## 使用Sequential Thinking

配置成功后，您可以在Augment Agent中：
- 要求进行复杂的推理任务
- 使用步骤化思维过程
- 获得更详细的问题分析

## 高级配置

如果需要自定义参数，可以在args数组中添加：
```json
{
    "name": "sequential-thinking",
    "command": "npx",
    "args": [
        "@modelcontextprotocol/server-sequential-thinking",
        "--custom-param",
        "value"
    ]
}
```
