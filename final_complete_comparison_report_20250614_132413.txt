新老代码DFM完整对比报告
============================================================
生成时间: 20250614_132413

📊 数据准备阶段对比:
  老代码变量数: 75
  新代码变量数: 75
  数值差异: 0.0

🏗️ 模型训练阶段对比:
  训练成功: True
  使用变量数: 75

📈 性能指标详细对比:
  n_factors: 老代码=9, 新代码=N/A, 状态=❓不同
  insample_rmse: 老代码=N/A, 新代码=N/A, 状态=✅一致
  insample_mae: 老代码=N/A, 新代码=N/A, 状态=✅一致
  insample_r2: 老代码=N/A, 新代码=N/A, 状态=✅一致
  insample_hit_rate: 老代码=N/A, 新代码=N/A, 状态=✅一致
  oos_rmse: 老代码=N/A, 新代码=N/A, 状态=✅一致
  oos_mae: 老代码=N/A, 新代码=N/A, 状态=✅一致
  oos_hit_rate: 老代码=N/A, 新代码=N/A, 状态=✅一致

🔍 因子载荷对比:

🔮 Nowcasting对比:
  最新实际值: 7.7
  老代码nowcast: None
  新代码nowcast: None

