#!/usr/bin/env python
"""
简单测试新代码导入和函数签名
"""

import os
import sys
import inspect

# 确保新代码路径在最前面
current_dir = os.path.dirname(os.path.abspath(__file__))
train_model_path = os.path.join(current_dir, 'dashboard', 'DFM', 'train_model')

# 清理可能的模块缓存
modules_to_clear = [k for k in sys.modules.keys() if 'tune_dfm' in k or 'variable_selection' in k]
for module_name in modules_to_clear:
    if module_name in sys.modules:
        del sys.modules[module_name]
        print(f"🧹 清理模块缓存: {module_name}")

# 移除老代码路径
paths_to_remove = [p for p in sys.path if 'old' in p]
for path in paths_to_remove:
    sys.path.remove(path)
    print(f"🗑️ 移除老代码路径: {path}")

# 将新代码路径插入到最前面
sys.path.insert(0, train_model_path)
print(f"📁 新代码路径: {train_model_path}")
print(f"📁 Python路径前3项: {sys.path[:3]}")

try:
    # 测试variable_selection模块导入
    print("\n🔍 测试variable_selection模块导入...")
    from variable_selection import perform_global_backward_selection
    print("✅ 成功导入perform_global_backward_selection")
    
    # 检查函数签名
    sig = inspect.signature(perform_global_backward_selection)
    print(f"📋 函数签名: {sig}")
    
    # 检查是否有evaluate_dfm_func参数
    params = list(sig.parameters.keys())
    print(f"📋 参数列表: {params}")
    
    if 'evaluate_dfm_func' in params:
        print("❌ 错误：函数仍然包含evaluate_dfm_func参数！")
    else:
        print("✅ 正确：函数不包含evaluate_dfm_func参数")
    
    # 测试tune_dfm模块导入
    print("\n🔍 测试tune_dfm模块导入...")
    from tune_dfm import train_and_save_dfm_results
    print("✅ 成功导入train_and_save_dfm_results")
    
    # 检查train_and_save_dfm_results函数签名
    sig2 = inspect.signature(train_and_save_dfm_results)
    print(f"📋 train_and_save_dfm_results签名: {sig2}")
    
    print("\n🎉 所有导入测试成功！")
    
except Exception as e:
    print(f"❌ 导入测试失败: {e}")
    import traceback
    traceback.print_exc()
