#!/usr/bin/env python3
"""
测试新代码训练过程
只运行新代码，验证训练流程是否正常
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import traceback

# 添加路径
sys.path.append('dashboard/DFM/train_model')

def test_new_code_training():
    """测试新代码的训练过程"""
    
    print("=" * 80)
    print("🚀 测试新代码训练过程")
    print("=" * 80)
    
    try:
        # 1. 导入新代码模块
        print("\n📦 导入新代码模块...")
        from tune_dfm import train_and_save_dfm_results
        print("✅ 新代码模块导入成功")
        
        # 2. 准备测试数据
        print("\n📊 准备测试数据...")
        
        # 创建模拟数据
        np.random.seed(42)
        dates = pd.date_range('2020-01-01', '2024-12-31', freq='W')
        n_vars = 20
        
        # 生成相关的时间序列数据
        data = {}
        base_trend = np.cumsum(np.random.randn(len(dates)) * 0.01)
        
        for i in range(n_vars):
            # 添加一些相关性和噪声
            correlation = 0.3 + 0.4 * np.random.rand()
            noise = np.random.randn(len(dates)) * 0.05
            data[f'var_{i:02d}'] = base_trend * correlation + noise
        
        # 创建目标变量（与其他变量有一定相关性）
        target_weights = np.random.rand(n_vars) * 0.1
        target_data = sum(data[f'var_{i:02d}'] * target_weights[i] for i in range(n_vars))
        target_data += np.random.randn(len(dates)) * 0.02
        data['target_var'] = target_data
        
        df = pd.DataFrame(data, index=dates)
        
        print(f"✅ 测试数据准备完成")
        print(f"   数据维度: {df.shape}")
        print(f"   时间范围: {df.index.min()} 到 {df.index.max()}")
        print(f"   变量列表: {list(df.columns)[:5]}... (共{len(df.columns)}个)")
        
        # 3. 设置训练参数
        print("\n⚙️ 设置训练参数...")
        
        # 选择部分变量进行训练
        selected_indicators = [f'var_{i:02d}' for i in range(15)]  # 选择前15个变量
        target_variable = 'target_var'
        
        # 设置时间范围
        training_start_date = '2020-01-01'
        training_end_date = '2024-06-30'
        validation_start_date = '2024-07-01'
        validation_end_date = '2024-12-31'
        
        # 训练参数 - 启用所有功能进行完整测试
        training_params = {
            'input_df': df,
            'target_variable': target_variable,
            'selected_indicators': selected_indicators,
            'training_start_date': training_start_date,
            'training_end_date': training_end_date,
            'validation_start_date': validation_start_date,
            'validation_end_date': validation_end_date,
            'n_factors': 5,
            'em_max_iter': 20,  # 减少迭代次数以加快测试
            'enable_hyperparameter_tuning': True,  # 启用超参数调优
            'enable_variable_selection': True,  # 启用变量选择
            'variable_selection_method': 'global_backward',  # 使用全局后向选择
            'max_workers': 2,  # 使用2个工作进程
            'enable_detailed_analysis': True,  # 启用详细分析
            'generate_excel_report': True,  # 启用Excel报告生成
            'output_base_dir': 'test_output'
        }
        
        print("✅ 训练参数设置完成")
        print(f"   目标变量: {target_variable}")
        print(f"   选择变量数: {len(selected_indicators)}")
        print(f"   训练期: {training_start_date} 到 {training_end_date}")
        print(f"   验证期: {validation_start_date} 到 {validation_end_date}")
        print(f"   因子数: {training_params['n_factors']}")
        print(f"   EM迭代次数: {training_params['em_max_iter']}")
        print(f"   超参数调优: {training_params['enable_hyperparameter_tuning']}")
        print(f"   变量选择: {training_params['enable_variable_selection']}")
        print(f"   变量选择方法: {training_params['variable_selection_method']}")
        print(f"   详细分析: {training_params['enable_detailed_analysis']}")
        print(f"   Excel报告: {training_params['generate_excel_report']}")
        print(f"   工作进程数: {training_params['max_workers']}")
        
        # 4. 开始训练
        print("\n🚀 开始新代码训练...")
        print("-" * 60)
        
        def progress_callback(message):
            """进度回调函数"""
            print(f"[新代码] {message}")
        
        training_params['progress_callback'] = progress_callback
        
        # 执行训练
        start_time = datetime.now()
        results = train_and_save_dfm_results(**training_params)
        end_time = datetime.now()
        
        print("-" * 60)
        print(f"✅ 新代码训练完成!")
        print(f"   训练耗时: {end_time - start_time}")
        print(f"   返回结果类型: {type(results)}")
        
        if isinstance(results, dict):
            print(f"   结果键: {list(results.keys())}")
            for key, value in results.items():
                if isinstance(value, str) and os.path.exists(value):
                    print(f"   {key}: {value} ✅")
                else:
                    print(f"   {key}: {value}")
        
        print("\n🎉 新代码测试成功!")
        return True
        
    except Exception as e:
        print(f"\n❌ 新代码测试失败:")
        print(f"错误类型: {type(e).__name__}")
        print(f"错误信息: {str(e)}")
        print(f"\n详细错误堆栈:")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_new_code_training()
    if success:
        print("\n" + "=" * 80)
        print("🎉 新代码测试通过!")
        print("=" * 80)
        sys.exit(0)
    else:
        print("\n" + "=" * 80)
        print("❌ 新代码测试失败!")
        print("=" * 80)
        sys.exit(1)
