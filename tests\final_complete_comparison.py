#!/usr/bin/env python
"""
最终完整的新老代码DFM对比脚本
- 使用正确的85个变量
- 完整运行所有流程：数据准备、变量选择、模型估计、性能评估
- 详细比较：因子载荷、RMSE、Hit Rate、MAE、nowcasting值
"""

import os
import sys
import pandas as pd
import numpy as np
import pickle
import joblib
from datetime import datetime
import traceback
import warnings
warnings.filterwarnings('ignore')

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
old_dir = os.path.join(project_root, 'old')
new_dir = os.path.join(project_root, 'dashboard', 'DFM')

if old_dir not in sys.path:
    sys.path.insert(0, old_dir)

for subdir in ['data_prep', 'train_model']:
    subdir_path = os.path.join(new_dir, subdir)
    if subdir_path not in sys.path:
        sys.path.insert(0, subdir_path)

def run_complete_workflow():
    """运行完整的新老代码工作流对比"""
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    print(f"🚀 最终完整的新老代码DFM对比 - {timestamp}")
    print("="*80)
    
    # 步骤1：数据准备对比
    print("\n📊 步骤1：数据准备对比")
    print("-"*50)
    
    data_results = compare_data_preparation()
    if not data_results['success']:
        print("❌ 数据准备阶段失败，终止测试")
        return
    
    old_data = data_results['old_data']
    new_data = data_results['new_data']
    target_var = '规模以上工业增加值:当月同比'
    
    # 步骤2：模型训练对比
    print("\n🏗️ 步骤2：模型训练对比")
    print("-"*50)
    
    model_results = compare_model_training(old_data, new_data, target_var)
    if not model_results['success']:
        print("❌ 模型训练阶段失败")
        return
    
    # 步骤3：详细性能对比
    print("\n📈 步骤3：详细性能对比")
    print("-"*50)
    
    performance_results = compare_performance_metrics(
        model_results['old_results'], 
        model_results['new_results'],
        old_data, target_var
    )
    
    # 步骤4：因子载荷对比
    print("\n🔍 步骤4：因子载荷对比")
    print("-"*50)
    
    loadings_results = compare_factor_loadings(
        model_results['old_results'], 
        model_results['new_results']
    )
    
    # 步骤5：Nowcasting对比
    print("\n🔮 步骤5：Nowcasting对比")
    print("-"*50)
    
    nowcast_results = compare_nowcasting_values(
        model_results['old_results'], 
        model_results['new_results'],
        old_data, target_var
    )
    
    # 步骤6：生成完整报告
    print("\n📝 步骤6：生成完整报告")
    print("-"*50)
    
    generate_final_report({
        'timestamp': timestamp,
        'data_results': data_results,
        'model_results': model_results,
        'performance_results': performance_results,
        'loadings_results': loadings_results,
        'nowcast_results': nowcast_results
    })
    
    print("\n🎉 完整对比测试成功完成！")

def compare_data_preparation():
    """对比数据准备阶段"""
    
    try:
        # 运行老代码数据准备
        print("  🔄 运行老代码数据准备...")
        from data_preparation import prepare_data as old_prepare_data
        import config as old_config
        
        old_result = old_prepare_data(
            excel_path='data/经济数据库0508.xlsx',
            target_sheet_name='工业增加值同比增速_月度_同花顺',
            target_variable_name='规模以上工业增加值:当月同比',
            target_freq='W-FRI',
            consecutive_nan_threshold=old_config.CONSECUTIVE_NAN_THRESHOLD if old_config.REMOVE_VARS_WITH_CONSECUTIVE_NANS else None,
            data_start_date=getattr(old_config, 'DATA_START_DATE', None),
            data_end_date=getattr(old_config, 'DATA_END_DATE', None)
        )
        old_data = old_result[0]
        old_predictor_vars = [col for col in old_data.columns if col != '规模以上工业增加值:当月同比']
        print(f"    ✅ 老代码: {len(old_predictor_vars)}个预测变量")
        
        # 运行新代码数据准备
        print("  🔄 运行新代码数据准备...")
        # 临时添加新代码路径
        import sys
        original_path = sys.path.copy()
        try:
            new_data_prep_path = os.path.join(project_root, 'dashboard', 'DFM', 'data_prep')
            if new_data_prep_path not in sys.path:
                sys.path.insert(0, new_data_prep_path)
            
            from data_preparation import prepare_data as new_prepare_data
            new_result = new_prepare_data(
                excel_path='data/经济数据库0508.xlsx',
                target_sheet_name='工业增加值同比增速_月度_同花顺',
                target_variable_name='规模以上工业增加值:当月同比',
                target_freq='W-FRI',
                consecutive_nan_threshold=10,  # 启用连续缺失值过滤
                data_start_date='2020-01-01',
                data_end_date=None
            )
            new_data = new_result[0]
            new_predictor_vars = [col for col in new_data.columns if col != '规模以上工业增加值:当月同比']
            print(f"    ✅ 新代码: {len(new_predictor_vars)}个预测变量")
        finally:
            sys.path = original_path
        
        # 对比分析
        common_vars = set(old_predictor_vars) & set(new_predictor_vars)
        old_only = set(old_predictor_vars) - set(new_predictor_vars)
        new_only = set(new_predictor_vars) - set(old_predictor_vars)
        
        print(f"  📋 数据准备对比结果:")
        print(f"    老代码变量数: {len(old_predictor_vars)}")
        print(f"    新代码变量数: {len(new_predictor_vars)}")
        print(f"    共同变量数: {len(common_vars)}")
        print(f"    老代码独有: {len(old_only)}")
        print(f"    新代码独有: {len(new_only)}")
        
        if len(old_only) > 0:
            print(f"    老代码独有变量: {list(old_only)[:3]}...")
        if len(new_only) > 0:
            print(f"    新代码独有变量: {list(new_only)[:3]}...")
        
        # 数值一致性检查
        if len(common_vars) > 0:
            numerical_diff = 0
            for var in list(common_vars)[:5]:  # 检查前5个共同变量
                if var in old_data.columns and var in new_data.columns:
                    common_index = old_data.index.intersection(new_data.index)
                    if len(common_index) > 0:
                        old_values = old_data.loc[common_index, var].fillna(0)
                        new_values = new_data.loc[common_index, var].fillna(0)
                        diff = np.sum(np.abs(old_values - new_values))
                        numerical_diff += diff
            print(f"    数值差异总和: {numerical_diff:.10f}")
        
        return {
            'success': True,
            'old_data': old_data,
            'new_data': new_data,
            'old_predictor_count': len(old_predictor_vars),
            'new_predictor_count': len(new_predictor_vars),
            'common_count': len(common_vars),
            'numerical_diff': numerical_diff if 'numerical_diff' in locals() else 0
        }
        
    except Exception as e:
        print(f"    ❌ 数据准备对比失败: {e}")
        traceback.print_exc()
        return {'success': False, 'error': str(e)}

def compare_model_training(old_data, new_data, target_var):
    """对比模型训练阶段"""
    
    try:
        # 准备训练数据（使用75个变量）
        predictor_vars = [col for col in old_data.columns if col != target_var]
        training_data = old_data.copy()
        
        print(f"  📊 使用{len(predictor_vars)}个预测变量进行模型训练")
        print(f"  📅 训练期: 2020-01-01 to 2024-06-28")
        print(f"  📅 验证期: 2024-06-29 to 2024-12-27")
        
        # 准备训练期数据
        train_start = pd.to_datetime('2020-01-01')
        train_end = pd.to_datetime('2024-06-28')
        train_data = training_data[(training_data.index >= train_start) & (training_data.index <= train_end)]
        
        # 🔥 收集初始基准信息
        initial_baseline_info = {
            'old': {'initial_variables': len(predictor_vars), 'initial_baseline_score': None},
            'new': {'initial_variables': len(predictor_vars), 'initial_baseline_score': None}
        }
        
        # 运行老代码模型训练
        print("  🔄 运行老代码模型训练...")
        old_results = run_old_model_training(train_data, target_var)
        if old_results:
            print(f"    ✅ 老代码训练成功")
            print(f"    📊 因子数: {old_results.get('n_factors', 'unknown')}")
            if 'Lambda' in old_results and old_results['Lambda'] is not None:
                print(f"    📊 Lambda形状: {old_results['Lambda'].shape}")
            else:
                print(f"    📊 Lambda形状: N/A")
            
            # 收集老代码初始基准信息
            if 'initial_baseline_info' in old_results:
                initial_baseline_info['old'] = old_results['initial_baseline_info']
                print(f"    📊 初始基准得分(HR): {initial_baseline_info['old'].get('initial_baseline_hr', 'N/A')}")
                print(f"    📊 初始基准得分(RMSE): {initial_baseline_info['old'].get('initial_baseline_rmse', 'N/A')}")
                print(f"    📊 初始变量数: {initial_baseline_info['old'].get('initial_variables', 'N/A')}")
        else:
            print("    ❌ 老代码训练失败")
        
        # 运行新代码模型训练
        print("  🔄 运行新代码模型训练...")
        new_results = run_new_model_training(training_data, target_var, predictor_vars)
        if new_results:
            print(f"    ✅ 新代码训练成功")
            # 提取新代码的关键信息
            try:
                metadata_path = 'dashboard/DFM/outputs/models/final_dfm_metadata.pkl'
                model_path = 'dashboard/DFM/outputs/models/final_dfm_model.joblib'
                
                # 读取元数据
                if os.path.exists(metadata_path):
                    with open(metadata_path, 'rb') as f:
                        metadata = pickle.load(f)
                    n_factors = metadata.get('n_factors', 'unknown')
                    print(f"    📊 因子数: {n_factors}")
                    
                    # 🔥 收集新代码初始基准信息
                    if 'initial_baseline_info' in metadata:
                        initial_baseline_info['new'] = metadata['initial_baseline_info']
                        print(f"    📊 初始基准得分(HR): {initial_baseline_info['new'].get('initial_baseline_hr', 'N/A')}")
                        print(f"    📊 初始基准得分(RMSE): {initial_baseline_info['new'].get('initial_baseline_rmse', 'N/A')}")
                        print(f"    📊 初始变量数: {initial_baseline_info['new'].get('initial_variables', 'N/A')}")
                    
                    # 提取所有性能指标
                    performance_metrics = {}
                    if 'evaluation_results' in metadata:
                        eval_res = metadata['evaluation_results']
                        performance_metrics.update({
                            'insample_rmse': eval_res.get('insample_rmse'),
                            'insample_mae': eval_res.get('insample_mae'),
                            'insample_r2': eval_res.get('insample_r2'),
                            'insample_hit_rate': eval_res.get('insample_hit_rate'),
                            'oos_rmse': eval_res.get('oos_rmse'),
                            'oos_mae': eval_res.get('oos_mae'),
                            'oos_hit_rate': eval_res.get('oos_hit_rate')
                        })
                        print(f"    📊 样本内RMSE: {performance_metrics.get('insample_rmse', 'N/A')}")
                        print(f"    📊 样本外RMSE: {performance_metrics.get('oos_rmse', 'N/A')}")
                        print(f"    📊 样本外Hit Rate: {performance_metrics.get('oos_hit_rate', 'N/A')}")
                    
                    # 读取模型文件获取因子载荷
                    Lambda = None
                    if os.path.exists(model_path):
                        try:
                            dfm_model = joblib.load(model_path)
                            if hasattr(dfm_model, 'Lambda'):
                                Lambda = dfm_model.Lambda
                                print(f"    📊 Lambda形状: {Lambda.shape}")
                        except Exception as e:
                            print(f"    ⚠️ 载荷矩阵读取失败: {e}")
                    
                    # 整合所有结果
                    new_results.update({
                        'n_factors': n_factors,
                        'metadata': metadata,
                        'Lambda': Lambda,
                        'success': True,
                        **performance_metrics
                    })
                    
            except Exception as e:
                print(f"    ⚠️ 新代码结果提取失败: {e}")
                print(f"    📊 因子数: unknown")
        else:
            print("    ❌ 新代码训练失败")
        
        # 🔥 输出初始基准得分对比
        print("  🎯 初始基准得分对比:")
        print("  " + "="*60)
        print(f"  {'指标':<25} {'老代码':<15} {'新代码':<15}")
        print("  " + "-"*60)

        # 🔥 修复：确保 initial_baseline_info 不为 None
        if initial_baseline_info is None:
            initial_baseline_info = {'old': {}, 'new': {}}

        old_info = initial_baseline_info.get('old', {})
        new_info = initial_baseline_info.get('new', {})

        old_vars = old_info.get('initial_variables', 'N/A')
        new_vars = new_info.get('initial_variables', 'N/A')
        print(f"  {'初始变量数':<25} {old_vars:<15} {new_vars:<15}")

        old_hr = old_info.get('initial_baseline_hr', None)
        new_hr = new_info.get('initial_baseline_hr', None)
        old_hr_str = f"{old_hr:.2f}%" if old_hr is not None and np.isfinite(old_hr) else "N/A"
        new_hr_str = f"{new_hr:.2f}%" if new_hr is not None and np.isfinite(new_hr) else "N/A"
        print(f"  {'初始基准HR':<25} {old_hr_str:<15} {new_hr_str:<15}")

        old_rmse = old_info.get('initial_baseline_rmse', None)
        new_rmse = new_info.get('initial_baseline_rmse', None)
        old_rmse_str = f"{old_rmse:.6f}" if old_rmse is not None and np.isfinite(old_rmse) else "N/A"
        new_rmse_str = f"{new_rmse:.6f}" if new_rmse is not None and np.isfinite(new_rmse) else "N/A"
        print(f"  {'初始基准RMSE':<25} {old_rmse_str:<15} {new_rmse_str:<15}")

        print("  " + "="*60)
        
        return {
            'success': True,
            'old_results': old_results,
            'new_results': new_results,
            'training_data': training_data,
            'predictor_count': len(predictor_vars),
            'initial_baseline_info': initial_baseline_info  # 🔥 新增初始基准信息
        }
        
    except Exception as e:
        print(f"    ❌ 模型训练对比失败: {e}")
        traceback.print_exc()
        return {'success': False, 'error': str(e)}

def run_old_model_training(train_data, target_var, n_factors=9):
    """运行老代码模型训练 - 🔥 直接调用老代码run_tuning()"""
    try:
        print(f"      🔄 开始老代码完整模型训练...")
        
        # 直接导入并调用老代码的run_tuning函数
        import sys
        import os
        import importlib
        
        # 确保老代码目录在sys.path中
        old_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'old')
        if old_dir not in sys.path:
            sys.path.insert(0, old_dir)
        
        # 导入老代码的tune_dfm模块
        try:
            # 🔥 强制清理所有相关模块缓存
            modules_to_clear = [k for k in sys.modules.keys() if 'tune_dfm' in k or 'old' in k]
            for module_name in modules_to_clear:
                del sys.modules[module_name]
                print(f"      🧹 清理模块缓存: {module_name}")
            
            # 修改工作目录到old目录以确保相对导入正确
            original_cwd = os.getcwd()
            original_sys_path = sys.path.copy()
            
            try:
                os.chdir(old_dir)
                sys.path.insert(0, old_dir)
                
                # 🔥 直接导入并检查老代码模块
                print(f"      📁 当前工作目录: {os.getcwd()}")
                print(f"      📁 老代码目录: {old_dir}")
                
                import tune_dfm
                
                print(f"      ✅ 成功导入老代码tune_dfm模块")
                print(f"      📍 模块文件路径: {tune_dfm.__file__}")
                
                # 检查run_tuning函数是否存在
                if hasattr(tune_dfm, 'run_tuning'):
                    print(f"      ✅ 确认run_tuning函数存在")
                    print(f"      📋 run_tuning函数类型: {type(tune_dfm.run_tuning)}")
                    
                    # 🔥 直接调用老代码的run_tuning函数
                    print(f"      🔄 执行老代码run_tuning()...")
                    tune_dfm.run_tuning()
                    print(f"      ✅ 老代码run_tuning()执行完成")
                    
                else:
                    print(f"      ❌ run_tuning函数不存在")
                    print(f"      📋 可用属性: {[attr for attr in dir(tune_dfm) if not attr.startswith('_')][:10]}...")
                    
                    # 检查是否有train_and_save_dfm_results（这可能是错误导入了新代码）
                    if hasattr(tune_dfm, 'train_and_save_dfm_results'):
                        print(f"      ⚠️ 警告：检测到train_and_save_dfm_results函数，这可能是新代码！")
                        print(f"      🚨 模块路径检查: {tune_dfm.__file__}")
                        return None
                    
                    return None
                
            finally:
                # 恢复原始工作目录和path
                os.chdir(original_cwd)
                sys.path = original_sys_path
            
            # 收集老代码的训练结果
            old_results = collect_old_training_results(train_data, target_var)
            
            return old_results
            
        except Exception as e:
            print(f"      ❌ 老代码训练执行失败: {e}")
            import traceback
            traceback.print_exc()
            return None
            
    except Exception as e:
        print(f"      ❌ 老代码训练失败: {e}")
        traceback.print_exc()
        return None

def collect_old_training_results(train_data, target_var):
    """收集老代码的训练结果"""
    try:
        print(f"      📊 收集老代码训练结果...")
        
        # 查找老代码的结果文件
        old_result_files = [
            'old/dym_estimate/dfm_result/final_dfm_metadata.pkl',
            'old/dym_estimate/dfm_result/final_dfm_model.joblib',
            'dym_estimate/dfm_result/final_dfm_metadata.pkl',
            'dym_estimate/dfm_result/final_dfm_model.joblib'
        ]
        
        metadata = None
        dfm_model = None
        
        # 尝试加载元数据
        for metadata_file in [f for f in old_result_files if 'metadata.pkl' in f]:
            if os.path.exists(metadata_file):
                try:
                    with open(metadata_file, 'rb') as f:
                        metadata = pickle.load(f)
                    print(f"      ✅ 成功加载老代码元数据: {metadata_file}")
                    break
                except Exception as e:
                    print(f"      ⚠️ 加载{metadata_file}失败: {e}")
                    continue
        
        # 尝试加载模型
        for model_file in [f for f in old_result_files if 'model.joblib' in f]:
            if os.path.exists(model_file):
                try:
                    import joblib
                    dfm_model = joblib.load(model_file)
                    print(f"      ✅ 成功加载老代码模型: {model_file}")
                    break
                except Exception as e:
                    print(f"      ⚠️ 加载{model_file}失败: {e}")
                    continue
        
        # 构建结果字典
        old_results = {
            'model_type': 'old_dfm',
            'success': True,
            'metadata': metadata,
            'dfm_model': dfm_model
        }
        
        # 🔥 从元数据中提取关键信息
        if metadata:
            old_results['n_factors'] = metadata.get('best_k_factors', metadata.get('n_factors', 'unknown'))
            
            # 提取初始基准信息
            predictor_count = len([col for col in train_data.columns if col != target_var])
            old_results['initial_baseline_info'] = {
                'initial_variables': predictor_count,
                'initial_baseline_hr': metadata.get('initial_baseline_hr'),
                'initial_baseline_rmse': metadata.get('initial_baseline_rmse')
            }
            
            # 提取性能指标
            if 'evaluation_results' in metadata:
                eval_res = metadata['evaluation_results']
                old_results.update({
                    'insample_rmse': eval_res.get('insample_rmse'),
                    'insample_mae': eval_res.get('insample_mae'),
                    'insample_r2': eval_res.get('insample_r2'),
                    'insample_hit_rate': eval_res.get('insample_hit_rate'),
                    'oos_rmse': eval_res.get('oos_rmse'),
                    'oos_mae': eval_res.get('oos_mae'),
                    'oos_hit_rate': eval_res.get('oos_hit_rate')
                })
            
            # 提取因子载荷
            if 'training_only_lambda' in metadata:
                old_results['Lambda'] = metadata['training_only_lambda']
            elif dfm_model and hasattr(dfm_model, 'Lambda'):
                old_results['Lambda'] = dfm_model.Lambda
            
            # 提取因子序列
            if 'training_only_factors_ts' in metadata:
                old_results['factors'] = metadata['training_only_factors_ts']
            elif dfm_model and hasattr(dfm_model, 'x_sm'):
                old_results['factors'] = dfm_model.x_sm
                old_results['x_sm'] = dfm_model.x_sm  # 兼容性
        
        print(f"      📊 老代码结果汇总:")
        print(f"        因子数: {old_results.get('n_factors', 'N/A')}")
        print(f"        初始变量数: {old_results.get('initial_baseline_info', {}).get('initial_variables', 'N/A')}")
        if old_results.get('Lambda') is not None:
            print(f"        Lambda形状: {old_results['Lambda'].shape if hasattr(old_results['Lambda'], 'shape') else 'N/A'}")
        
        return old_results
        
    except Exception as e:
        print(f"      ❌ 收集老代码结果失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def run_new_model_training(training_data, target_var, predictor_vars):
    """运行新代码模型训练"""
    try:
        # 导入新代码的训练函数
        import sys
        import os
        
        # 添加dashboard路径到sys.path
        dashboard_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'dashboard')
        train_model_path = os.path.join(dashboard_path, 'DFM', 'train_model')
        
        if dashboard_path not in sys.path:
            sys.path.insert(0, dashboard_path)
        if train_model_path not in sys.path:
            sys.path.insert(0, train_model_path)
        
        # 尝试导入新代码的训练函数
        try:
            from DFM.train_model.tune_dfm import train_and_save_dfm_results
        except ImportError:
            # 如果上面失败，尝试直接导入
            from tune_dfm import train_and_save_dfm_results
        
        # 设置新代码训练参数
        training_params = {
            'input_df': training_data,
            'target_variable': target_var,
            'selected_indicators': predictor_vars,
            'training_start_date': '2020-01-01',
            'training_end_date': '2024-06-28',
            'validation_start_date': '2024-06-29',
            'validation_end_date': '2024-12-27',
            'n_factors': 10,  # 会被Bai-Ng覆盖
            'em_max_iter': 30,
            'enable_hyperparameter_tuning': False,
            'use_bai_ng_factor_selection': True,  # 启用Bai-Ng
            'enable_variable_selection': True,    # 🔧 启用变量选择！
            'variable_selection_method': 'global_backward',  # 使用全局后向剔除
            'enable_detailed_analysis': False,
            'generate_excel_report': False,
            'output_base_dir': 'dashboard/DFM/outputs'
        }
        
        # 运行新代码训练
        results = train_and_save_dfm_results(**training_params)
        
        return results
        
    except Exception as e:
        print(f"      新代码训练详细错误: {e}")
        traceback.print_exc()
        return None

def compare_performance_metrics(old_results, new_results, training_data, target_var):
    """对比性能指标"""
    
    try:
        print("  📊 计算老代码性能指标...")
        old_metrics = calculate_old_performance(old_results, training_data, target_var)
        
        print("  📊 计算新代码性能指标...")
        new_metrics = calculate_new_performance(new_results)
        
        # 详细对比
        print("  📋 性能指标详细对比:")
        print("  " + "="*80)
        print(f"  {'指标':<25} {'老代码':<20} {'新代码':<20} {'差异':<10} {'状态'}")
        print("  " + "-"*80)
        
        key_metrics = [
            ('n_factors', '因子数'),
            ('insample_rmse', '样本内RMSE'),
            ('insample_mae', '样本内MAE'),
            ('insample_r2', '样本内R²'),
            ('insample_hit_rate', '样本内Hit Rate'),
            ('oos_rmse', '样本外RMSE'),
            ('oos_mae', '样本外MAE'),
            ('oos_hit_rate', '样本外Hit Rate')
        ]
        
        comparison_results = {}
        for metric_key, metric_name in key_metrics:
            old_val = old_metrics.get(metric_key, 'N/A')
            new_val = new_metrics.get(metric_key, 'N/A')
            
            # 计算差异
            diff_str = "N/A"
            if isinstance(old_val, (int, float)) and isinstance(new_val, (int, float)):
                if metric_key == 'n_factors':
                    diff_str = f"{new_val - old_val:+d}"
                elif metric_key in ['insample_hit_rate', 'oos_hit_rate'] and old_val != 0:
                    # Hit Rate以百分点显示差异
                    diff_pct = (new_val - old_val) * 100
                    diff_str = f"{diff_pct:+.1f}pp"
                elif old_val != 0:
                    # 其他指标以百分比显示差异
                    diff_pct = ((new_val - old_val) / old_val) * 100
                    diff_str = f"{diff_pct:+.1f}%"
                else:
                    diff_str = f"{new_val - old_val:+.4f}"
            
            # 状态判断
            if isinstance(old_val, (int, float)) and isinstance(new_val, (int, float)):
                if metric_key in ['insample_rmse', 'insample_mae', 'oos_rmse', 'oos_mae']:
                    if new_val < old_val * 0.95:  # 显著改善(>5%)
                        status = "✅显著改善"
                    elif new_val < old_val:
                        status = "✅小幅改善"
                    elif new_val > old_val * 1.05:  # 显著恶化(>5%)
                        status = "❌显著恶化"
                    else:
                        status = "❌小幅恶化"
                elif metric_key in ['insample_r2', 'insample_hit_rate', 'oos_hit_rate']:
                    if new_val > old_val * 1.05:  # 显著改善(>5%)
                        status = "✅显著改善"
                    elif new_val > old_val:
                        status = "✅小幅改善"
                    elif new_val < old_val * 0.95:  # 显著恶化(>5%)
                        status = "❌显著恶化"
                    else:
                        status = "❌小幅恶化"
                else:
                    status = "✅一致" if new_val == old_val else "❓不同"
            else:
                status = "✅一致" if str(old_val) == str(new_val) else "❓不同"
            
            # 格式化数值显示
            old_str = f"{old_val:.4f}" if isinstance(old_val, float) else str(old_val)
            new_str = f"{new_val:.4f}" if isinstance(new_val, float) else str(new_val)
            
            print(f"  {metric_name:<25} {old_str:<20} {new_str:<20} {diff_str:<10} {status}")
            
            comparison_results[metric_key] = {
                'old': old_val,
                'new': new_val,
                'difference': diff_str,
                'status': status
            }
        
        return {
            'success': True,
            'old_metrics': old_metrics,
            'new_metrics': new_metrics,
            'comparison': comparison_results
        }
        
    except Exception as e:
        print(f"    ❌ 性能指标对比失败: {e}")
        return {'success': False, 'error': str(e)}

def calculate_old_performance(old_results, training_data, target_var):
    """计算老代码性能指标"""
    if not old_results:
        return {}
    
    try:
        metrics = {}
        
        # 基本信息
        metrics['n_factors'] = old_results.get('n_factors', 'unknown')
        
        # 如果有必要的数据，计算性能指标
        if 'Lambda' in old_results and 'x_sm' in old_results:
            Lambda = old_results['Lambda']
            factors = old_results['x_sm']
            
            # 准备数据
            train_start = pd.to_datetime('2020-01-01')
            train_end = pd.to_datetime('2024-06-28')
            train_data = training_data[(training_data.index >= train_start) & (training_data.index <= train_end)]
            
            y = train_data[target_var].dropna()
            
            if len(y) > 0 and hasattr(factors, 'shape') and len(factors.shape) > 1:
                # 找到目标变量在Lambda中的位置
                target_idx = list(training_data.columns).index(target_var)
                if target_idx < Lambda.shape[0]:
                    target_loadings = Lambda[target_idx, :]
                    
                    # 计算拟合值
                    min_len = min(len(factors), len(y))
                    if min_len > 0:
                        fitted_values = factors[:min_len] @ target_loadings
                        y_subset = y.iloc[:min_len]
                        
                        # 计算样本内指标
                        residuals = y_subset - fitted_values
                        metrics['insample_rmse'] = np.sqrt(np.mean(residuals**2))
                        metrics['insample_mae'] = np.mean(np.abs(residuals))
                        metrics['insample_r2'] = 1 - np.var(residuals) / np.var(y_subset)
                        
                        # Hit Rate
                        if len(y_subset) > 1:
                            y_changes = np.sign(np.diff(y_subset))
                            fitted_changes = np.sign(np.diff(fitted_values))
                            hit_rate = np.mean(y_changes == fitted_changes)
                            metrics['insample_hit_rate'] = hit_rate
        
        return metrics
        
    except Exception as e:
        print(f"      计算老代码性能失败: {e}")
        return {}

def calculate_new_performance(new_results):
    """计算新代码性能指标"""
    if not new_results:
        return {}
    
    try:
        metrics = {}
        
        # 从new_results中直接提取
        if 'n_factors' in new_results:
            metrics['n_factors'] = new_results['n_factors']
        
        # 从元数据中提取
        if 'metadata' in new_results:
            metadata = new_results['metadata']
            if isinstance(metadata, dict):
                for key in ['n_factors', 'insample_rmse', 'insample_mae', 'insample_r2', 
                           'insample_hit_rate', 'oos_rmse', 'oos_mae', 'oos_hit_rate']:
                    if key in metadata:
                        metrics[key] = metadata[key]
                
                # 检查评估结果
                if 'evaluation_results' in metadata:
                    eval_results = metadata['evaluation_results']
                    if isinstance(eval_results, dict):
                        metrics.update(eval_results)
        
        # 尝试从元数据文件读取（备用）
        if not metrics:
            metadata_path = 'dashboard/DFM/outputs/models/final_dfm_metadata.pkl'
            if os.path.exists(metadata_path):
                with open(metadata_path, 'rb') as f:
                    metadata = pickle.load(f)
                
                # 提取关键指标
                if isinstance(metadata, dict):
                    for key in ['n_factors', 'insample_rmse', 'insample_mae', 'insample_r2', 
                               'insample_hit_rate', 'oos_rmse', 'oos_mae', 'oos_hit_rate']:
                        if key in metadata:
                            metrics[key] = metadata[key]
                    
                    # 检查评估结果
                    if 'evaluation_results' in metadata:
                        eval_results = metadata['evaluation_results']
                        if isinstance(eval_results, dict):
                            metrics.update(eval_results)
        
        return metrics
        
    except Exception as e:
        print(f"      计算新代码性能失败: {e}")
        return {}

def compare_factor_loadings(old_results, new_results):
    """对比因子载荷矩阵"""
    
    try:
        print("  📊 因子载荷矩阵对比...")
        
        # 提取老代码因子载荷
        old_loadings = None
        if old_results and 'Lambda' in old_results:
            old_loadings = old_results['Lambda']
            print(f"    老代码Lambda形状: {old_loadings.shape}")
        
        # 提取新代码因子载荷
        new_loadings = None
        try:
            model_path = 'dashboard/DFM/outputs/models/final_dfm_model.joblib'
            if os.path.exists(model_path):
                dfm_model = joblib.load(model_path)
                if hasattr(dfm_model, 'Lambda'):
                    new_loadings = dfm_model.Lambda
                    print(f"    新代码Lambda形状: {new_loadings.shape}")
        except:
            print("    ⚠️ 无法加载新代码因子载荷")
        
        # 对比分析
        comparison = {}
        if old_loadings is not None and new_loadings is not None:
            if old_loadings.shape == new_loadings.shape:
                # 计算相关性
                correlations = []
                for i in range(min(old_loadings.shape[1], new_loadings.shape[1])):
                    corr = np.corrcoef(old_loadings[:, i], new_loadings[:, i])[0, 1]
                    correlations.append(corr)
                
                comparison['shape_match'] = True
                comparison['factor_correlations'] = correlations
                comparison['mean_correlation'] = np.mean(np.abs(correlations))
                
                print(f"    ✅ 载荷矩阵形状匹配")
                print(f"    📊 因子相关性 (平均): {comparison['mean_correlation']:.4f}")
            else:
                comparison['shape_match'] = False
                print(f"    ❌ 载荷矩阵形状不匹配")
        else:
            comparison['available'] = False
            print(f"    ⚠️ 无法获取完整的因子载荷数据")
        
        return comparison
        
    except Exception as e:
        print(f"    ❌ 因子载荷对比失败: {e}")
        return {'success': False, 'error': str(e)}

def compare_nowcasting_values(old_results, new_results, training_data, target_var):
    """对比Nowcasting值"""
    
    try:
        print("  🔮 Nowcasting值对比...")
        
        # 获取最新可用的目标变量值作为参考
        latest_actual = training_data[target_var].dropna().iloc[-1]
        latest_date = training_data[target_var].dropna().index[-1]
        
        print(f"    📅 最新实际值: {latest_actual:.2f} ({latest_date.strftime('%Y-%m-%d')})")
        
        # 计算老代码nowcasting（简化版）
        old_nowcast = None
        if old_results and 'x_sm' in old_results and 'Lambda' in old_results:
            try:
                factors = old_results['x_sm']
                Lambda = old_results['Lambda']
                target_idx = list(training_data.columns).index(target_var)
                
                if target_idx < Lambda.shape[0] and len(factors) > 0:
                    target_loadings = Lambda[target_idx, :]
                    last_factors = factors[-1, :] if len(factors.shape) > 1 else factors[-1]
                    old_nowcast = np.dot(last_factors, target_loadings)
                    print(f"    📊 老代码nowcast: {old_nowcast:.2f}")
            except Exception as e:
                print(f"    ⚠️ 老代码nowcast计算失败: {e}")
        
        # 获取新代码nowcasting结果
        new_nowcast = None
        try:
            # 检查是否有现成的nowcasting结果
            nowcast_path = 'dashboard/DFM/outputs/nowcast_evolution/latest_nowcast.csv'
            if os.path.exists(nowcast_path):
                nowcast_df = pd.read_csv(nowcast_path, index_col=0, parse_dates=True)
                if target_var in nowcast_df.columns:
                    new_nowcast = nowcast_df[target_var].iloc[-1]
                    print(f"    📊 新代码nowcast: {new_nowcast:.2f}")
            else:
                print(f"    ⚠️ 新代码nowcast文件不存在")
        except Exception as e:
            print(f"    ⚠️ 新代码nowcast读取失败: {e}")
        
        # 对比分析
        comparison = {
            'latest_actual': latest_actual,
            'latest_date': latest_date,
            'old_nowcast': old_nowcast,
            'new_nowcast': new_nowcast
        }
        
        if old_nowcast is not None and new_nowcast is not None:
            diff = abs(old_nowcast - new_nowcast)
            old_error = abs(old_nowcast - latest_actual)
            new_error = abs(new_nowcast - latest_actual)
            
            comparison['nowcast_diff'] = diff
            comparison['old_error'] = old_error
            comparison['new_error'] = new_error
            
            print(f"    📊 Nowcast差异: {diff:.4f}")
            print(f"    📊 老代码误差: {old_error:.4f}")
            print(f"    📊 新代码误差: {new_error:.4f}")
            
            if new_error < old_error:
                print(f"    ✅ 新代码nowcast更准确")
            elif old_error < new_error:
                print(f"    ❌ 老代码nowcast更准确")
            else:
                print(f"    ⚡ Nowcast准确性相当")
        
        return comparison
        
    except Exception as e:
        print(f"    ❌ Nowcasting对比失败: {e}")
        return {'success': False, 'error': str(e)}

def generate_final_report(all_results):
    """生成最终完整报告"""
    
    timestamp = all_results['timestamp']
    
    try:
        report_filename = f'final_complete_comparison_report_{timestamp}.txt'
        
        with open(report_filename, 'w', encoding='utf-8') as f:
            f.write("新老代码DFM完整对比报告\n")
            f.write("="*60 + "\n")
            f.write(f"生成时间: {timestamp}\n\n")
            
            # 数据准备结果
            if 'data_results' in all_results:
                data_res = all_results['data_results']
                f.write("📊 数据准备阶段对比:\n")
                f.write(f"  老代码变量数: {data_res.get('old_predictor_count', 'N/A')}\n")
                f.write(f"  新代码变量数: {data_res.get('new_predictor_count', 'N/A')}\n")
                f.write(f"  数值差异: {data_res.get('numerical_diff', 'N/A')}\n\n")
            
            # 模型训练结果
            if 'model_results' in all_results:
                model_res = all_results['model_results']
                f.write("🏗️ 模型训练阶段对比:\n")
                f.write(f"  训练成功: {model_res.get('success', False)}\n")
                f.write(f"  使用变量数: {model_res.get('predictor_count', 'N/A')}\n\n")
            
            # 性能指标对比
            if 'performance_results' in all_results and all_results['performance_results'].get('success'):
                perf_res = all_results['performance_results']
                f.write("📈 性能指标详细对比:\n")
                for metric_key, metric_data in perf_res.get('comparison', {}).items():
                    f.write(f"  {metric_key}: 老代码={metric_data['old']}, 新代码={metric_data['new']}, 状态={metric_data['status']}\n")
                f.write("\n")
            
            # 因子载荷对比
            if 'loadings_results' in all_results:
                loadings_res = all_results['loadings_results']
                f.write("🔍 因子载荷对比:\n")
                if 'mean_correlation' in loadings_res:
                    f.write(f"  平均相关性: {loadings_res['mean_correlation']:.4f}\n")
                f.write("\n")
            
            # Nowcasting对比
            if 'nowcast_results' in all_results:
                nowcast_res = all_results['nowcast_results']
                f.write("🔮 Nowcasting对比:\n")
                f.write(f"  最新实际值: {nowcast_res.get('latest_actual', 'N/A')}\n")
                f.write(f"  老代码nowcast: {nowcast_res.get('old_nowcast', 'N/A')}\n")
                f.write(f"  新代码nowcast: {nowcast_res.get('new_nowcast', 'N/A')}\n")
                if 'nowcast_diff' in nowcast_res:
                    f.write(f"  差异: {nowcast_res['nowcast_diff']:.4f}\n")
                f.write("\n")
        
        print(f"  📄 完整报告已保存: {report_filename}")
        
        # 保存详细结果数据
        results_filename = f'final_complete_comparison_data_{timestamp}.pkl'
        with open(results_filename, 'wb') as f:
            pickle.dump(all_results, f)
        print(f"  💾 详细数据已保存: {results_filename}")
        
    except Exception as e:
        print(f"  ❌ 报告生成失败: {e}")

if __name__ == '__main__':
    run_complete_workflow() 