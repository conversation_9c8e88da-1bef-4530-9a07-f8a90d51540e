--- 开始详细调优日志 (Run: 20250614_123756) ---
输出目录: dym_estimate\dfm_result
配置: 两阶段流程
  阶段1: 全局后向变量筛选 (固定 k=块数 N, 优化 HR -> -RMSE)
  阶段2: 因子选择 (方法=bai_ng, 阈值=Drop<0.1)
*** 完整模式：使用所有变量进行筛选 ***

--- 全局后向筛选开始 ---
初始预测变量数: 75
初始基准得分 (HR, -RMSE): (np.float64(77.70270270270271), np.float64(-1.3178820959499833))
Iter 1: 移除 '硫酸钾：开工率：中国（周）'，得分 (HR=77.70%, RMSE=1.317882) -> (HR=83.45%, RMSE=1.209177)
Iter 2: 移除 'PP：产能利用率：中国（周）'，得分 (HR=83.45%, RMSE=1.209177) -> (HR=85.81%, RMSE=1.268357)
Iter 3: 未找到更优移除，筛选结束。

--- 全局后向筛选结束 ---
最终预测变量数: 73
最终得分 (HR, -RMSE): (np.float64(85.8108108108108), np.float64(-1.2683573236445558))

--- 阶段 1 结果 (全局筛选) ---
起始变量范围: 全部 76 个变量
固定因子数 (N): 10
最佳评分 (HR, -RMSE): (np.float64(85.8108108108108), np.float64(-1.2683573236445558))
最终预测变量数量: 73

--- 阶段 2 结果 ---
因子选择方法: bai_ng (基于 PCA)
最终选择因子数: 9
(x_sm) 已是 DataFrame (Shape: (280, 9))，列名符合预期。
  Loadings (Lambda) 已从 NumPy 转换为 DataFrame (Shape: (74, 9))。
开始计算行业 Pooled R²...
  处理行业: '化纤' (9 个变量)...
  行业 '化纤' Pooled R²: 0.8746 (基于 9 个变量)
  处理行业: '橡胶塑料' (7 个变量)...
  行业 '橡胶塑料' Pooled R²: 0.6762 (基于 7 个变量)
  处理行业: '化学化工' (19 个变量)...
  行业 '化学化工' Pooled R²: 0.9906 (基于 19 个变量)
  处理行业: '煤炭' (13 个变量)...
  行业 '煤炭' Pooled R²: 0.7545 (基于 13 个变量)
  处理行业: '钢铁' (12 个变量)...
  行业 '钢铁' Pooled R²: 0.3981 (基于 12 个变量)
  处理行业: '油气' (3 个变量)...
  行业 '油气' Pooled R²: 0.8778 (基于 3 个变量)
  处理行业: '汽车' (2 个变量)...
  行业 '汽车' Pooled R²: 0.6863 (基于 2 个变量)
  处理行业: '电力' (3 个变量)...
  行业 '电力' Pooled R²: 0.2844 (基于 3 个变量)
  处理行业: 'PMI' (5 个变量)...
  行业 'PMI' Pooled R²: 0.6496 (基于 5 个变量)
  处理行业: '工业增加值' (1 个变量)...
  行业 '工业增加值' Pooled R²: 0.6024 (基于 1 个变量)
开始计算单因子对行业的 Pooled R²...
--- 计算因子: Factor1 ---
  处理行业: '化纤' (9 个变量) 对 Factor1...
  => Factor1 对行业 '化纤' 的 Pooled R²: 0.3169 (基于 9 个变量)
  处理行业: '橡胶塑料' (7 个变量) 对 Factor1...
  => Factor1 对行业 '橡胶塑料' 的 Pooled R²: 0.0454 (基于 7 个变量)
  处理行业: '化学化工' (19 个变量) 对 Factor1...
  => Factor1 对行业 '化学化工' 的 Pooled R²: 0.5324 (基于 19 个变量)
  处理行业: '煤炭' (13 个变量) 对 Factor1...
  => Factor1 对行业 '煤炭' 的 Pooled R²: 0.0751 (基于 13 个变量)
  处理行业: '钢铁' (12 个变量) 对 Factor1...
  => Factor1 对行业 '钢铁' 的 Pooled R²: 0.1954 (基于 12 个变量)
  处理行业: '油气' (3 个变量) 对 Factor1...
  => Factor1 对行业 '油气' 的 Pooled R²: 0.1504 (基于 3 个变量)
  处理行业: '汽车' (2 个变量) 对 Factor1...
  => Factor1 对行业 '汽车' 的 Pooled R²: 0.1824 (基于 2 个变量)
  处理行业: '电力' (3 个变量) 对 Factor1...
  => Factor1 对行业 '电力' 的 Pooled R²: 0.0112 (基于 3 个变量)
  处理行业: 'PMI' (5 个变量) 对 Factor1...
  => Factor1 对行业 'PMI' 的 Pooled R²: 0.1661 (基于 5 个变量)
  处理行业: '工业增加值' (1 个变量) 对 Factor1...
  => Factor1 对行业 '工业增加值' 的 Pooled R²: 0.0916 (基于 1 个变量)
--- 计算因子: Factor2 ---
  处理行业: '化纤' (9 个变量) 对 Factor2...
  => Factor2 对行业 '化纤' 的 Pooled R²: 0.1855 (基于 9 个变量)
  处理行业: '橡胶塑料' (7 个变量) 对 Factor2...
  => Factor2 对行业 '橡胶塑料' 的 Pooled R²: 0.1595 (基于 7 个变量)
  处理行业: '化学化工' (19 个变量) 对 Factor2...
  => Factor2 对行业 '化学化工' 的 Pooled R²: 0.2968 (基于 19 个变量)
  处理行业: '煤炭' (13 个变量) 对 Factor2...
  => Factor2 对行业 '煤炭' 的 Pooled R²: 0.2112 (基于 13 个变量)
  处理行业: '钢铁' (12 个变量) 对 Factor2...
  => Factor2 对行业 '钢铁' 的 Pooled R²: 0.0051 (基于 12 个变量)
  处理行业: '油气' (3 个变量) 对 Factor2...
  => Factor2 对行业 '油气' 的 Pooled R²: 0.1020 (基于 3 个变量)
  处理行业: '汽车' (2 个变量) 对 Factor2...
  => Factor2 对行业 '汽车' 的 Pooled R²: 0.0267 (基于 2 个变量)
  处理行业: '电力' (3 个变量) 对 Factor2...
  => Factor2 对行业 '电力' 的 Pooled R²: 0.0003 (基于 3 个变量)
  处理行业: 'PMI' (5 个变量) 对 Factor2...
  => Factor2 对行业 'PMI' 的 Pooled R²: 0.0437 (基于 5 个变量)
  处理行业: '工业增加值' (1 个变量) 对 Factor2...
  => Factor2 对行业 '工业增加值' 的 Pooled R²: 0.3470 (基于 1 个变量)
--- 计算因子: Factor3 ---
  处理行业: '化纤' (9 个变量) 对 Factor3...
  => Factor3 对行业 '化纤' 的 Pooled R²: 0.1223 (基于 9 个变量)
  处理行业: '橡胶塑料' (7 个变量) 对 Factor3...
  => Factor3 对行业 '橡胶塑料' 的 Pooled R²: 0.2244 (基于 7 个变量)
  处理行业: '化学化工' (19 个变量) 对 Factor3...
  => Factor3 对行业 '化学化工' 的 Pooled R²: 0.0036 (基于 19 个变量)
  处理行业: '煤炭' (13 个变量) 对 Factor3...
  => Factor3 对行业 '煤炭' 的 Pooled R²: 0.2458 (基于 13 个变量)
  处理行业: '钢铁' (12 个变量) 对 Factor3...
  => Factor3 对行业 '钢铁' 的 Pooled R²: 0.0029 (基于 12 个变量)
  处理行业: '油气' (3 个变量) 对 Factor3...
  => Factor3 对行业 '油气' 的 Pooled R²: 0.3365 (基于 3 个变量)
  处理行业: '汽车' (2 个变量) 对 Factor3...
  => Factor3 对行业 '汽车' 的 Pooled R²: 0.2111 (基于 2 个变量)
  处理行业: '电力' (3 个变量) 对 Factor3...
  => Factor3 对行业 '电力' 的 Pooled R²: 0.0720 (基于 3 个变量)
  处理行业: 'PMI' (5 个变量) 对 Factor3...
  => Factor3 对行业 'PMI' 的 Pooled R²: 0.0406 (基于 5 个变量)
  处理行业: '工业增加值' (1 个变量) 对 Factor3...
  => Factor3 对行业 '工业增加值' 的 Pooled R²: 0.0001 (基于 1 个变量)
--- 计算因子: Factor4 ---
  处理行业: '化纤' (9 个变量) 对 Factor4...
  => Factor4 对行业 '化纤' 的 Pooled R²: 0.0158 (基于 9 个变量)
  处理行业: '橡胶塑料' (7 个变量) 对 Factor4...
  => Factor4 对行业 '橡胶塑料' 的 Pooled R²: 0.0408 (基于 7 个变量)
  处理行业: '化学化工' (19 个变量) 对 Factor4...
  => Factor4 对行业 '化学化工' 的 Pooled R²: 0.0016 (基于 19 个变量)
  处理行业: '煤炭' (13 个变量) 对 Factor4...
  => Factor4 对行业 '煤炭' 的 Pooled R²: 0.0521 (基于 13 个变量)
  处理行业: '钢铁' (12 个变量) 对 Factor4...
  => Factor4 对行业 '钢铁' 的 Pooled R²: 0.0018 (基于 12 个变量)
  处理行业: '油气' (3 个变量) 对 Factor4...
  => Factor4 对行业 '油气' 的 Pooled R²: 0.0304 (基于 3 个变量)
  处理行业: '汽车' (2 个变量) 对 Factor4...
  => Factor4 对行业 '汽车' 的 Pooled R²: 0.0051 (基于 2 个变量)
  处理行业: '电力' (3 个变量) 对 Factor4...
  => Factor4 对行业 '电力' 的 Pooled R²: 0.0053 (基于 3 个变量)
  处理行业: 'PMI' (5 个变量) 对 Factor4...
  => Factor4 对行业 'PMI' 的 Pooled R²: 0.0475 (基于 5 个变量)
  处理行业: '工业增加值' (1 个变量) 对 Factor4...
  => Factor4 对行业 '工业增加值' 的 Pooled R²: 0.0228 (基于 1 个变量)
--- 计算因子: Factor5 ---
  处理行业: '化纤' (9 个变量) 对 Factor5...
  => Factor5 对行业 '化纤' 的 Pooled R²: 0.0550 (基于 9 个变量)
  处理行业: '橡胶塑料' (7 个变量) 对 Factor5...
  => Factor5 对行业 '橡胶塑料' 的 Pooled R²: 0.0089 (基于 7 个变量)
  处理行业: '化学化工' (19 个变量) 对 Factor5...
  => Factor5 对行业 '化学化工' 的 Pooled R²: 0.0003 (基于 19 个变量)
  处理行业: '煤炭' (13 个变量) 对 Factor5...
  => Factor5 对行业 '煤炭' 的 Pooled R²: 0.0869 (基于 13 个变量)
  处理行业: '钢铁' (12 个变量) 对 Factor5...
  => Factor5 对行业 '钢铁' 的 Pooled R²: 0.0540 (基于 12 个变量)
  处理行业: '油气' (3 个变量) 对 Factor5...
  => Factor5 对行业 '油气' 的 Pooled R²: 0.0508 (基于 3 个变量)
  处理行业: '汽车' (2 个变量) 对 Factor5...
  => Factor5 对行业 '汽车' 的 Pooled R²: 0.0487 (基于 2 个变量)
  处理行业: '电力' (3 个变量) 对 Factor5...
  => Factor5 对行业 '电力' 的 Pooled R²: 0.0592 (基于 3 个变量)
  处理行业: 'PMI' (5 个变量) 对 Factor5...
  => Factor5 对行业 'PMI' 的 Pooled R²: 0.0867 (基于 5 个变量)
  处理行业: '工业增加值' (1 个变量) 对 Factor5...
  => Factor5 对行业 '工业增加值' 的 Pooled R²: 0.0187 (基于 1 个变量)
--- 计算因子: Factor6 ---
  处理行业: '化纤' (9 个变量) 对 Factor6...
  => Factor6 对行业 '化纤' 的 Pooled R²: 0.0162 (基于 9 个变量)
  处理行业: '橡胶塑料' (7 个变量) 对 Factor6...
  => Factor6 对行业 '橡胶塑料' 的 Pooled R²: 0.0551 (基于 7 个变量)
  处理行业: '化学化工' (19 个变量) 对 Factor6...
  => Factor6 对行业 '化学化工' 的 Pooled R²: 0.0040 (基于 19 个变量)
  处理行业: '煤炭' (13 个变量) 对 Factor6...
  => Factor6 对行业 '煤炭' 的 Pooled R²: 0.1575 (基于 13 个变量)
  处理行业: '钢铁' (12 个变量) 对 Factor6...
  => Factor6 对行业 '钢铁' 的 Pooled R²: 0.0190 (基于 12 个变量)
  处理行业: '油气' (3 个变量) 对 Factor6...
  => Factor6 对行业 '油气' 的 Pooled R²: 0.0295 (基于 3 个变量)
  处理行业: '汽车' (2 个变量) 对 Factor6...
  => Factor6 对行业 '汽车' 的 Pooled R²: 0.0017 (基于 2 个变量)
  处理行业: '电力' (3 个变量) 对 Factor6...
  => Factor6 对行业 '电力' 的 Pooled R²: 0.0267 (基于 3 个变量)
  处理行业: 'PMI' (5 个变量) 对 Factor6...
  => Factor6 对行业 'PMI' 的 Pooled R²: 0.1899 (基于 5 个变量)
  处理行业: '工业增加值' (1 个变量) 对 Factor6...
  => Factor6 对行业 '工业增加值' 的 Pooled R²: 0.0027 (基于 1 个变量)
--- 计算因子: Factor7 ---
  处理行业: '化纤' (9 个变量) 对 Factor7...
  => Factor7 对行业 '化纤' 的 Pooled R²: 0.0200 (基于 9 个变量)
  处理行业: '橡胶塑料' (7 个变量) 对 Factor7...
  => Factor7 对行业 '橡胶塑料' 的 Pooled R²: 0.0369 (基于 7 个变量)
  处理行业: '化学化工' (19 个变量) 对 Factor7...
  => Factor7 对行业 '化学化工' 的 Pooled R²: 0.0197 (基于 19 个变量)
  处理行业: '煤炭' (13 个变量) 对 Factor7...
  => Factor7 对行业 '煤炭' 的 Pooled R²: 0.1138 (基于 13 个变量)
  处理行业: '钢铁' (12 个变量) 对 Factor7...
  => Factor7 对行业 '钢铁' 的 Pooled R²: 0.0510 (基于 12 个变量)
  处理行业: '油气' (3 个变量) 对 Factor7...
  => Factor7 对行业 '油气' 的 Pooled R²: 0.0166 (基于 3 个变量)
  处理行业: '汽车' (2 个变量) 对 Factor7...
  => Factor7 对行业 '汽车' 的 Pooled R²: 0.0267 (基于 2 个变量)
  处理行业: '电力' (3 个变量) 对 Factor7...
  => Factor7 对行业 '电力' 的 Pooled R²: 0.0137 (基于 3 个变量)
  处理行业: 'PMI' (5 个变量) 对 Factor7...
  => Factor7 对行业 'PMI' 的 Pooled R²: 0.0151 (基于 5 个变量)
  处理行业: '工业增加值' (1 个变量) 对 Factor7...
  => Factor7 对行业 '工业增加值' 的 Pooled R²: 0.0419 (基于 1 个变量)
--- 计算因子: Factor8 ---
  处理行业: '化纤' (9 个变量) 对 Factor8...
  => Factor8 对行业 '化纤' 的 Pooled R²: 0.0258 (基于 9 个变量)
  处理行业: '橡胶塑料' (7 个变量) 对 Factor8...
  => Factor8 对行业 '橡胶塑料' 的 Pooled R²: 0.0220 (基于 7 个变量)
  处理行业: '化学化工' (19 个变量) 对 Factor8...
  => Factor8 对行业 '化学化工' 的 Pooled R²: 0.1053 (基于 19 个变量)
  处理行业: '煤炭' (13 个变量) 对 Factor8...
  => Factor8 对行业 '煤炭' 的 Pooled R²: 0.0211 (基于 13 个变量)
  处理行业: '钢铁' (12 个变量) 对 Factor8...
  => Factor8 对行业 '钢铁' 的 Pooled R²: 0.0326 (基于 12 个变量)
  处理行业: '油气' (3 个变量) 对 Factor8...
  => Factor8 对行业 '油气' 的 Pooled R²: 0.0126 (基于 3 个变量)
  处理行业: '汽车' (2 个变量) 对 Factor8...
  => Factor8 对行业 '汽车' 的 Pooled R²: 0.0726 (基于 2 个变量)
  处理行业: '电力' (3 个变量) 对 Factor8...
  => Factor8 对行业 '电力' 的 Pooled R²: 0.0013 (基于 3 个变量)
  处理行业: 'PMI' (5 个变量) 对 Factor8...
  => Factor8 对行业 'PMI' 的 Pooled R²: 0.0090 (基于 5 个变量)
  处理行业: '工业增加值' (1 个变量) 对 Factor8...
  => Factor8 对行业 '工业增加值' 的 Pooled R²: 0.0337 (基于 1 个变量)
--- 计算因子: Factor9 ---
  处理行业: '化纤' (9 个变量) 对 Factor9...
  => Factor9 对行业 '化纤' 的 Pooled R²: 0.1155 (基于 9 个变量)
  处理行业: '橡胶塑料' (7 个变量) 对 Factor9...
  => Factor9 对行业 '橡胶塑料' 的 Pooled R²: 0.0156 (基于 7 个变量)
  处理行业: '化学化工' (19 个变量) 对 Factor9...
  => Factor9 对行业 '化学化工' 的 Pooled R²: 0.0437 (基于 19 个变量)
  处理行业: '煤炭' (13 个变量) 对 Factor9...
  => Factor9 对行业 '煤炭' 的 Pooled R²: 0.0777 (基于 13 个变量)
  处理行业: '钢铁' (12 个变量) 对 Factor9...
  => Factor9 对行业 '钢铁' 的 Pooled R²: 0.0253 (基于 12 个变量)
  处理行业: '油气' (3 个变量) 对 Factor9...
  => Factor9 对行业 '油气' 的 Pooled R²: 0.0371 (基于 3 个变量)
  处理行业: '汽车' (2 个变量) 对 Factor9...
  => Factor9 对行业 '汽车' 的 Pooled R²: 0.0169 (基于 2 个变量)
  处理行业: '电力' (3 个变量) 对 Factor9...
  => Factor9 对行业 '电力' 的 Pooled R²: 0.0197 (基于 3 个变量)
  处理行业: 'PMI' (5 个变量) 对 Factor9...
  => Factor9 对行业 'PMI' 的 Pooled R²: 0.1158 (基于 5 个变量)
  处理行业: '工业增加值' (1 个变量) 对 Factor9...
  => Factor9 对行业 '工业增加值' 的 Pooled R²: 0.0417 (基于 1 个变量)
[调试类型R2计算] 检查通过: var_type_map 有效 (大小: 126), 准备调用 calculate_factor_type_r2。
开始计算单因子对变量类型的 Pooled R² (OLS-based)...
有 6 个变量未能映射到已知类型，归入 '_未知类型_'。
将为 9 个因子和 6 个类型计算 Pooled R² (OLS-based)...
单因子对变量类型的 Pooled R² (OLS-based) 计算完成。
[调试类型R2计算] 检查通过: var_type_map 有效 (大小: 126), 准备调用 calculate_factor_type_r2。
开始计算单因子对变量类型的 Pooled R² (OLS-based)...
有 6 个变量未能映射到已知类型，归入 '_未知类型_'。
将为 9 个因子和 6 个类型计算 Pooled R² (OLS-based)...
单因子对变量类型的 Pooled R² (OLS-based) 计算完成。
成功提取最终模型状态转移矩阵 A 的特征根 (模长)，数量: 9
--- 调用 analyze_and_save_final_results ---
开始分析最终结果并写入 Excel: dym_estimate\dfm_result\final_results_20250614_123756.xlsx
计算对月底目标的 Nowcast 序列 y_{T(t)|t} (使用 final_dfm_results.x 和 A)...
Target date T(t)=2025-05-30 00:00:00 not in filtered_state index for t=2025-05-02 00:00:00. Calculating k based on days/7.
对月底目标的 Nowcast 序列 y_{T(t)|t} 计算完成。
计算平滑 Nowcast 序列 (使用 .x_sm, 用于对比)...
平滑 Nowcast 序列计算完成。
  [DEBUG] Filtered Nowcast (for metrics) Index Range: 2019-12-27 00:00:00 to 2025-05-02 00:00:00
Filtered Nowcast series (for reporting) to start from 2020-01-01. Shape: (279,)
计算最终模型的 IS/OOS RMSE 和 Hit Rate (使用 Filtered Nowcast 和 analysis_utils)...
最终模型评估指标 (基于 Filtered Nowcast) 计算完成: {'is_rmse': np.float64(1.8083570369196786), 'oos_rmse': np.float64(0.9553326247249742), 'is_mae': 1.3679239405066417, 'oos_mae': 0.7840909212273817, 'is_hit_rate': np.float64(70.54054054054055), 'oos_hit_rate': np.float64(76.0)}
准备写入 Excel 文件: dym_estimate\dfm_result\final_results_20250614_123756.xlsx
成功加载元数据: dym_estimate\dfm_result\final_dfm_metadata.pkl
  正在写入 '指标解释' Sheet...
  '指标解释' Sheet 写入完成。
  正在写入 'Summary' Sheet...
  'Summary' Sheet 写入完成。
  正在写入 'Monthly Forecast vs Target' Sheet...
开始创建对齐的 Nowcast vs Target 表格...
成功创建对齐表格，包含 66 行数据。
  'Monthly Forecast vs Target' Sheet 写入完成。
  正在写入 'Factor Time Series' Sheet...
  'Factor Time Series' Sheet 写入完成。
  正在写入 'R2 Analysis Combined' Sheet...
[Debug Write R2] Received 'factor_type_r2'. Type: <class 'dict'>. Is None or Empty: False
[Debug Write R2] 'factor_type_r2' keys: ['Factor1', 'Factor2', 'Factor3', 'Factor4', 'Factor5', 'Factor6', 'Factor7', 'Factor8', 'Factor9']
  'R2 Analysis Combined' Sheet 写入完成。
  正在写入 'Variables and Loadings' Sheet...
  'Variables and Loadings' Sheet 写入完成。
Excel 文件写入完成: dym_estimate\dfm_result\final_results_20250614_123756.xlsx
开始绘制最终图形到目录: dym_estimate\dfm_result\plots
  绘制 Filtered Nowcast vs Target 图...

[绘图函数] 生成最终 Nowcasting 图: dym_estimate\dfm_result\plots\20250614_123756_final_filtered_nowcast_vs_target.png...
  已屏蔽 1月/2月 的实际观测值用于绘图。
最终 Nowcasting 图已保存到: dym_estimate\dfm_result\plots\20250614_123756_final_filtered_nowcast_vs_target.png
  Filtered Nowcast vs Target 图绘制完成。
  绘制最终因子载荷聚类图...
\n[绘图函数] 开始生成因子载荷聚类热力图: dym_estimate\dfm_result\plots\20250614_123756_final_factor_loadings_clustermap.png...
变量数量较多 (>50) 且未指定 top_n_vars，默认禁用数值标注。
因子载荷聚类热力图已保存至: dym_estimate\dfm_result\plots\20250614_123756_final_factor_loadings_clustermap.png
  因子载荷聚类图绘制完成。
  绘制行业 vs 主要驱动因子图...
开始绘制行业与主要驱动因子对比图 (Log 处理行业变量(左轴), 原始因子(右轴))...
[绘图调试] 找到 11 个唯一行业: ['PMI', '化学化工', '化纤', '工业增加值', '橡胶塑料', '汽车', '油气', '煤炭', '电力', '运输']...
  未能确定行业 '运输' 的驱动因子 (可能所有因子 R² 都无效或为负)。
  [Plot Loop] 开始处理行业: PMI
  [Plot Loop] 完成处理行业: PMI
  [Plot Loop] 开始处理行业: 化学化工
  [Plot Loop] 完成处理行业: 化学化工
  [Plot Loop] 开始处理行业: 化纤
  [Plot Loop] 完成处理行业: 化纤
  [Plot Loop] 开始处理行业: 工业增加值
  [Plot Loop] 完成处理行业: 工业增加值
  [Plot Loop] 开始处理行业: 橡胶塑料
  [Plot Loop] 完成处理行业: 橡胶塑料
  [Plot Loop] 开始处理行业: 汽车
  [Plot Loop] 完成处理行业: 汽车
  [Plot Loop] 开始处理行业: 油气
  [Plot Loop] 完成处理行业: 油气
  [Plot Loop] 开始处理行业: 煤炭
  [Plot Loop] 完成处理行业: 煤炭
  [Plot Loop] 开始处理行业: 电力
  [Plot Loop] 完成处理行业: 电力
  [Plot Loop] 开始处理行业: 钢铁
  [Plot Loop] 完成处理行业: 钢铁
隐藏未使用的子图轴...
设置主标题...
调整布局 (tight_layout)...
布局调整完成。
即将保存行业驱动因子图到: dym_estimate\dfm_result\plots\20250614_123756_final_industry_driving_factors.png
行业与驱动因子对比图已保存至: dym_estimate\dfm_result\plots\20250614_123756_final_industry_driving_factors.png
关闭绘图对象...
绘制行业与驱动因子对比图完成。
  行业 vs 主要驱动因子图绘制完成。
最终结果分析和保存完成。
Added 'factor_loadings_df' to returned metrics.
Added 'nowcast_aligned' to returned metrics.
Added 'y_test_aligned' to returned metrics.
analyze_and_save_final_results 调用完成。返回的指标: {'is_rmse': np.float64(1.8083570369196786), 'oos_rmse': np.float64(0.9553326247249742), 'is_mae': 1.3679239405066417, 'oos_mae': 0.7840909212273817, 'is_hit_rate': np.float64(70.54054054054055), 'oos_hit_rate': np.float64(76.0), 'factor_loadings_df':                             Factor1   Factor2   Factor3  ...   Factor7   Factor8   Factor9
MEG：产能利用率：中国（周）           -0.074569  0.557042 -0.255650  ...  0.306339  0.830807 -0.138463
MEG：产量：中国（周）              -0.037333  0.073603 -0.134184  ...  0.056462  0.075975 -0.137386
PE：产能利用率：中国（周）            -0.022046  0.248053 -0.732556  ...  0.101865  0.801552 -0.345912
PE：化工生产企业：产量：中国（周）        -0.007686 -0.012333 -0.063505  ...  0.096040 -0.046846 -0.043366
PE：社会库存：中国（周）             -0.075263  0.129428 -0.221359  ...  0.064603  0.402030 -0.059015
...                             ...       ...       ...  ...       ...       ...       ...
精煤：样本洗煤厂（110家）：日均产量：中国（周）  0.245240 -0.433964  0.065412  ...  0.034216 -0.200951 -0.074914
聚酯：产能利用率：中国（周）             0.350530  0.122198  0.261601  ... -0.083528 -0.092762 -0.214443
聚酯：产量：中国（周）                0.032515 -0.021334  0.057562  ...  0.094762 -0.017221  0.066583
规模以上工业增加值:当月同比             0.191840  0.344490 -0.119019  ... -0.255985  0.242139 -0.395059
重点电厂:日耗量:煤炭               -0.090233 -0.010377 -0.344673  ...  0.236439  0.464360  0.071650

[74 rows x 9 columns], 'nowcast_aligned': 2019-12-27    3.167941
2020-01-03    3.082019
2020-01-10    2.053558
2020-01-17    1.873484
2020-01-24    3.103103
                ...   
2025-04-04    6.416710
2025-04-11    6.187263
2025-04-18    6.112761
2025-04-25    6.486271
2025-05-02    5.356587
Name: Nowcast, Length: 280, dtype: float64, 'y_test_aligned': 2019-12-27    NaN
2020-01-03    NaN
2020-01-10    NaN
2020-01-17    NaN
2020-01-24    NaN
             ... 
2025-04-04    7.7
2025-04-11    7.7
2025-04-18    7.7
2025-04-25    7.7
2025-05-02    NaN
Name: 规模以上工业增加值:当月同比, Length: 280, dtype: float64}
--- [Debug Meta Build Check] ---
[Debug Meta Build Check] Type of all_data_aligned_weekly: <class 'pandas.core.frame.DataFrame'>
[Debug Meta Build Check] all_data_aligned_weekly is None? False
[Debug Meta Build Check] Shape of all_data_aligned_weekly: (280, 76)
[Debug Meta Build Check] Type of target_mean_original: <class 'numpy.float64'>
[Debug Meta Build Check] target_mean_original is None? False
[Debug Meta Build Check] Value of target_mean_original: 5.043137254901961
[Debug Meta Build Check] Type of target_std_original: <class 'numpy.float64'>
[Debug Meta Build Check] target_std_original is None? False
[Debug Meta Build Check] Value of target_std_original: 2.5666518970799905
[Debug Meta Build Check] Type of final_data_processed: <class 'pandas.core.frame.DataFrame'>
[Debug Meta Build Check] final_data_processed is None? False
[Debug Meta Build Check] Shape of final_data_processed: (280, 74)
--- [Debug Meta Build Check End] ---
--- [Debug Final Save Check - pca_results_df specific] ---
[Debug Final Save Check] Type of pca_results_df IN METADATA before dump: <class 'pandas.core.frame.DataFrame'>
[Debug Final Save Check] Shape of pca_results_df IN METADATA before dump: (9, 4)
[Debug Final Save Check] Columns of pca_results_df IN METADATA before dump: ['主成分 (PC)', '解释方差 (%)', '累计解释方差 (%)', '特征值 (Eigenvalue)']
[Debug Final Save Check] '特征值 (Eigenvalue)' column EXISTS in pca_results_df.
--- [Debug Final Save Check - pca_results_df specific End] ---

--- 正在仅训练期数据上重新运行最终选定模型以进行稳定性分析 --- 
已截取训练期处理后数据 (最终变量列，截至 2024-06-28)，形状: (236, 74)
假设训练期数据已使用合适的（如全样本的）标准化参数处理。
开始在训练期数据上运行 DFM (k=9)...
训练期 DFM 模型实例化（即拟合）完成。
[Debug Train DFM] train_dfm_model type: <class 'DynamicFactorModel.DFMEMResultsWrapper'>
[Debug Train DFM] Attributes of train_dfm_model: ['A', 'B', 'Lambda', 'P0', 'Q', 'R', '__class__', '__delattr__', '__dict__', '__dir__', '__doc__', '__eq__', '__firstlineno__', '__format__', '__ge__', '__getattribute__', '__getstate__', '__gt__', '__hash__', '__init__', '__init_subclass__', '__le__', '__lt__', '__module__', '__ne__', '__new__', '__reduce__', '__reduce_ex__', '__repr__', '__setattr__', '__sizeof__', '__static_attributes__', '__str__', '__subclasshook__', '__weakref__', 'obs_mean', 'x', 'x0', 'x_sm', 'z']
[Debug Train DFM] Has 'x_sm' attribute? True
[Debug Train DFM] Has 'Lambda' attribute? True
[Debug Train DFM] Type of factors attribute: <class 'pandas.core.frame.DataFrame'>
[Debug Train DFM] Shape of factors: (236, 9)
[Debug Train DFM] Type of loadings attribute: <class 'numpy.ndarray'>
[Debug Train DFM] Shape of loadings: (74, 9)
已提取训练期因子时间序列 (来自 DataFrame)。
已提取训练期因子载荷矩阵 (来自 NumPy 数组)。
已将训练期载荷和因子序列添加到待保存的元数据中。
已将 'best_k_factors' (9) 添加到元数据。
开始保存最终模型到: dym_estimate\dfm_result\final_dfm_model.joblib
最终模型保存成功。
开始保存元数据到: dym_estimate\dfm_result\final_dfm_metadata.pkl
--- [Debug Final Save Check] ---
[Debug Final Save Check] Type of training_lambda IN METADATA before dump: <class 'pandas.core.frame.DataFrame'>
[Debug Final Save Check] Type of training_factors IN METADATA before dump: <class 'pandas.core.frame.DataFrame'>
[Debug Final Save Check] Shape of training_lambda IN METADATA before dump: (74, 9)
[Debug Final Save Check] Shape of training_factors IN METADATA before dump: (236, 9)
--- [Debug Final Save Check End] ---
[Debug Final Save Check] Added 'training_start_date' to metadata: 2020-01-01
已将 'x0' (initial_state) 和 'P0' (initial_state_cov) 添加到元数据。
元数据保存成功。

--- 调优和最终模型估计完成 --- 总耗时: 1513.73 秒 ---
Successfully imported results_analysis using absolute import
=== 启动完整DFM优化管道 ===
高级参数配置: {'factor_order': 1, 'idio_ar_order': 1, 'em_max_iter': 30, 'enable_hyperparameter_tuning': False, 'enable_variable_selection': True, 'variable_selection_method': 'global_backward', 'k_factors_range': (1, 8), 'max_workers': 4, 'validation_split_ratio': 0.8, 'enable_detailed_analysis': False, 'generate_excel_report': False, 'pca_n_components': 10, 'info_criterion_method': 'bic', 'cum_variance_threshold': 0.8}
结果将直接保存到顶层目录: dashboard/DFM/outputs
步骤1: 数据预处理...
步骤3: 验证数据对齐...
⚠️ 数据对齐验证出错，跳过: Cannot save file into a non-existent directory: 'dashboard\DFM\outputs\data'
步骤1.2: 继续数据预处理...
调试信息：用户选择的原始指标名称: ['中国:日均产量:生铁:重点企业', '中国:日均产量:粗钢:重点企业', '重点电厂:日耗量:煤炭', '中国:主要45港口:日均疏港量:铁矿石', '中国:生产率:焦炉:国内独立焦化厂(230家)', '中国:高炉开工率(247家)', '中国:产量:螺纹钢:主要钢厂', '中国:开工率:线材:主要钢厂', '中国:产量:线材:主要钢厂', '中国:产量:冷轧板卷:主要钢厂', '中国:产量:热轧板卷:主要钢厂', '中国:产量:中厚板:主要钢厂', '中国:产量:钢材:重点钢铁企业', '中国:开工率:汽车轮胎(半钢胎)', '中国:开工率:汽车轮胎(全钢胎)', '中国:开工率:精对苯二甲酸', '中国:江浙地区:开工率:涤纶长丝', '中国:装置负荷率:涤纶短纤', 'PTA：产能利用率：中国（周）', 'PTA：产量：中国（周）', 'MEG：产能利用率：中国（周）', 'MEG：产量：中国（周）', '聚酯：产能利用率：中国（周）', '聚酯：产量：中国（周）', '乙烯：MTO：生产企业：产能利用率：中国（周）', '乙烯：轻烃裂解：生产企业：产能利用率：中国（周）', '乙烯：石脑油裂解：生产企业：产能利用率：中国（周）', '乙烯：MTO：产量：中国（周）', '乙烯：产量：中国（周）', '乙烯：轻烃裂解：产量：中国（周）', '乙烯：石脑油裂解：产量：中国（周）', '乙烯：市场价：华东地区（周）', '氯化铵：产量：中国（周）', '氯化铵：产能利用率：中国（周）', '三聚氰胺：产量：中国（周）', '三聚氰胺：产能利用率：中国（周）', '尿素：产量：中国（周）', '尿素：产能利用率：中国（周）', '磷酸一铵：工业级：产能利用率：中国（周）', '磷酸一铵：工业级：产量：中国（周）', '磷酸一铵：产量：中国（周）', '磷酸二铵：产量：中国（周）', '硫酸钾：开工率：中国（周）', '氯化钾：产能利用率：中国（周）', 'PE：产能利用率：中国（周）', 'PE：化工生产企业：产量：中国（周）', 'PE：社会库存：中国（周）', 'PP：注塑：开工率：中国（周）', 'PP：产能利用率：中国（周）', 'PP：产量：中国（周）', 'PVC：产能利用率：中国（周）', 'PVC：产量：中国（周）', '中国:开工率(常减压开工率):山东地炼厂', '中国:产能利用率:成品油:独立炼厂', '中国:产能利用率:成品油:主营炼厂', '中国:开工率:产能(>200万吨):焦化企业(230家)', '中国:开工率:产能(100-200万吨):焦化企业(230家)', '中国:主流港口:库存量:煤炭', '中国:库存量:焦炭:国内样本钢厂(247家)', '动力煤：462家样本矿山：产能利用率（周）', '动力煤：462家样本矿山：日均产量（周）', '精煤：样本洗煤厂（110家）：日均产量：中国（周）', '煤炭：样本洗煤厂（110家）：开工率：中国（周）', '精煤：523家样本矿山：日均产量（周）', '原煤：523家样本矿山：日均产量（周）', '炼焦煤：523家样本矿山：开工率（周）', '焦炭：230家独立焦化厂：产能利用率：中国（周）', '焦炭：230家独立焦化厂：日均产量：中国（周）', '中国：可再生能源：发电量（月）', '中国：火力发电：发电量（月）', '制造业PMI', '制造业PMI:生产', '制造业PMI:新订单', '制造业PMI:新出口订单', '制造业PMI:从业人员']
调试信息：标准化后的指标名称: ['中国:日均产量:生铁:重点企业', '中国:日均产量:粗钢:重点企业', '重点电厂:日耗量:煤炭', '中国:主要45港口:日均疏港量:铁矿石', '中国:生产率:焦炉:国内独立焦化厂(230家)', '中国:高炉开工率(247家)', '中国:产量:螺纹钢:主要钢厂', '中国:开工率:线材:主要钢厂', '中国:产量:线材:主要钢厂', '中国:产量:冷轧板卷:主要钢厂', '中国:产量:热轧板卷:主要钢厂', '中国:产量:中厚板:主要钢厂', '中国:产量:钢材:重点钢铁企业', '中国:开工率:汽车轮胎(半钢胎)', '中国:开工率:汽车轮胎(全钢胎)', '中国:开工率:精对苯二甲酸', '中国:江浙地区:开工率:涤纶长丝', '中国:装置负荷率:涤纶短纤', 'pta:产能利用率:中国(周)', 'pta:产量:中国(周)', 'meg:产能利用率:中国(周)', 'meg:产量:中国(周)', '聚酯:产能利用率:中国(周)', '聚酯:产量:中国(周)', '乙烯:mto:生产企业:产能利用率:中国(周)', '乙烯:轻烃裂解:生产企业:产能利用率:中国(周)', '乙烯:石脑油裂解:生产企业:产能利用率:中国(周)', '乙烯:mto:产量:中国(周)', '乙烯:产量:中国(周)', '乙烯:轻烃裂解:产量:中国(周)', '乙烯:石脑油裂解:产量:中国(周)', '乙烯:市场价:华东地区(周)', '氯化铵:产量:中国(周)', '氯化铵:产能利用率:中国(周)', '三聚氰胺:产量:中国(周)', '三聚氰胺:产能利用率:中国(周)', '尿素:产量:中国(周)', '尿素:产能利用率:中国(周)', '磷酸一铵:工业级:产能利用率:中国(周)', '磷酸一铵:工业级:产量:中国(周)', '磷酸一铵:产量:中国(周)', '磷酸二铵:产量:中国(周)', '硫酸钾:开工率:中国(周)', '氯化钾:产能利用率:中国(周)', 'pe:产能利用率:中国(周)', 'pe:化工生产企业:产量:中国(周)', 'pe:社会库存:中国(周)', 'pp:注塑:开工率:中国(周)', 'pp:产能利用率:中国(周)', 'pp:产量:中国(周)', 'pvc:产能利用率:中国(周)', 'pvc:产量:中国(周)', '中国:开工率(常减压开工率):山东地炼厂', '中国:产能利用率:成品油:独立炼厂', '中国:产能利用率:成品油:主营炼厂', '中国:开工率:产能(>200万吨):焦化企业(230家)', '中国:开工率:产能(100-200万吨):焦化企业(230家)', '中国:主流港口:库存量:煤炭', '中国:库存量:焦炭:国内样本钢厂(247家)', '动力煤:462家样本矿山:产能利用率(周)', '动力煤:462家样本矿山:日均产量(周)', '精煤:样本洗煤厂(110家):日均产量:中国(周)', '煤炭:样本洗煤厂(110家):开工率:中国(周)', '精煤:523家样本矿山:日均产量(周)', '原煤:523家样本矿山:日均产量(周)', '炼焦煤:523家样本矿山:开工率(周)', '焦炭:230家独立焦化厂:产能利用率:中国(周)', '焦炭:230家独立焦化厂:日均产量:中国(周)', '中国:可再生能源:发电量(月)', '中国:火力发电:发电量(月)', '制造业pmi', '制造业pmi:生产', '制造业pmi:新订单', '制造业pmi:新出口订单', '制造业pmi:从业人员']
调试信息：数据中可用的列名（前10个）: ['规模以上工业增加值:当月同比', '中国:日均产量:生铁:重点企业', '中国:日均产量:粗钢:重点企业', '重点电厂:日耗量:煤炭', '中国:主要45港口:日均疏港量:铁矿石', '中国:生产率:焦炉:国内独立焦化厂(230家)', '中国:高炉开工率(247家)', '中国:产量:螺纹钢:主要钢厂', '中国:开工率:线材:主要钢厂', '中国:产量:线材:主要钢厂']
调试信息：数据总列数: 76
启用变量选择功能，将从用户选择的 76 个变量中进行 global_backward 筛选
使用手动设置的验证期（已检查未来日期）:
训练期: 2020-01-01 00:00:00 到 2024-06-28 00:00:00 (自动计算)
验证期: 2024-06-29 00:00:00 到 2024-12-27 00:00:00
未提供变量映射，尝试从文件加载...
从文件加载变量映射失败: name 'excel_file_path' is not defined，使用默认映射
============================================================
步骤2: 跳过初始因子数评估 (优化策略)
============================================================
  🚀 策略优化：变量筛选阶段固定使用 10 个因子 (与老代码一致)
  🚀 因子数评估将在变量筛选完成后进行 (k=1 到 8)
  🚀 预期节省时间：避免变量筛选过程中的重复因子数评估
============================================================
步骤2: 跳过超参数网格搜索 (用户禁用)
  直接使用指定的因子数量: 10
============================================================

============================================================
步骤3: 变量选择
============================================================
  🔍 变量选择条件调试:
    - enable_variable_selection: True
    - variable_selection_method: global_backward
    - len(all_variables): 76
    - best_params['k_factors']: 10
    - 最小要求变量数: 12
    - 条件1 (enable_variable_selection): True
    - 条件2 (len(all_variables) > k_factors + 2): 76 > 12 = True
    - 最终条件结果: True
----------------------------------------
  启用变量选择功能
  方法: global_backward
  初始变量数量: 76
  目标变量: 规模以上工业增加值:当月同比
  最优因子数: 10
----------------------------------------
  🔍 执行全局后向变量选择...
  这将逐步移除对模型贡献最小的变量
  优化目标: 最大化 Hit Rate，然后最小化 RMSE
  使用多进程并行计算加速选择过程

