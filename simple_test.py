#!/usr/bin/env python3
"""
简单测试新代码导入
"""

import sys
import os

print("开始简单测试...")

# 添加路径
sys.path.append('dashboard/DFM/train_model')

try:
    print("尝试导入 tune_dfm 模块...")
    import tune_dfm
    print("✅ tune_dfm 模块导入成功")
    
    # 检查主要函数是否存在
    if hasattr(tune_dfm, 'train_and_save_dfm_results'):
        print("✅ train_and_save_dfm_results 函数存在")
    else:
        print("❌ train_and_save_dfm_results 函数不存在")
        
    print("🎉 基本导入测试通过!")
    
except Exception as e:
    print(f"❌ 导入失败: {e}")
    import traceback
    traceback.print_exc()
